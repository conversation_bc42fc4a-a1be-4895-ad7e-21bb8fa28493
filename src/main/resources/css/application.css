/* 应用程序样式 */

/* 快速开始按钮样式 - 蓝色渐变圆形按钮 */
.quick-start-button {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-pref-width: 200px;
    -fx-pref-height: 200px;
    -fx-min-width: 200px;
    -fx-min-height: 200px;
    -fx-max-width: 200px;
    -fx-max-height: 200px;
    -fx-background-radius: 100px;
    -fx-border-radius: 100px;
    -fx-background-color: linear-gradient(to bottom, #2196F3, #1976D2);
    -fx-text-fill: #b4e8f3;
    -fx-effect: dropshadow(gaussian, rgba(17, 87, 149, 0.87), 10, 0, 0, 5);
    -fx-cursor: hand;
    -fx-alignment: center;
    -fx-content-display: center;
    -fx-text-alignment: center;
}

.quick-start-button:hover {
    -fx-background-color: linear-gradient(to bottom, #1976D2, #0D47A1);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.quick-start-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #0D47A1, #01579B);
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
}

/* 设置按钮样式 - SVG图标样式 */
.settings-button {
    -fx-pref-width: 30px;
    -fx-pref-height: 30px;
    -fx-min-width: 30px;
    -fx-min-height: 30px;
    -fx-max-width: 30px;
    -fx-max-height: 30px;
    -fx-background-color: rgba(255, 255, 255, 0.9);
    -fx-background-radius: 5px;
    -fx-border-color: rgba(0, 0, 0, 0.1);
    -fx-border-radius: 5px;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 3);
    -fx-cursor: hand;
    -fx-focus-traversable: true;
    -fx-alignment: center;
    /*-fx-content-display: center;*/
    /*-fx-text-alignment: center;*/
}

.settings-button:hover {
    -fx-background-color: rgba(255, 255, 255, 1.0);
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 10, 0, 0, 4);
}

.settings-button:pressed {
    -fx-background-color: rgba(240, 240, 240, 0.95);
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 5, 0, 0, 2);
}

/* 全屏按钮样式 */
.fullscreen-button {
    -fx-pref-width: 30px;
    -fx-pref-height: 30px;
    -fx-min-width: 30px;
    -fx-min-height: 30px;
    -fx-max-width: 30px;
    -fx-max-height: 30px;
    -fx-background-color: rgba(255, 255, 255, 0.9);
    -fx-background-radius: 5px;
    -fx-border-color: rgba(0, 0, 0, 0.1);
    -fx-border-radius: 5px;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 3);
    -fx-cursor: hand;
    -fx-focus-traversable: true;
    -fx-alignment: center;
}

.fullscreen-button:hover {
    -fx-background-color: rgba(255, 255, 255, 1.0);
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 10, 0, 0, 4);
}

.fullscreen-button:pressed {
    -fx-background-color: rgba(240, 240, 240, 0.95);
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 5, 0, 0, 2);
}

/* 退出全屏按钮样式 */
.exit-fullscreen-button {
    -fx-pref-width: 30px;
    -fx-pref-height: 30px;
    -fx-min-width: 30px;
    -fx-min-height: 30px;
    -fx-max-width: 30px;
    -fx-max-height: 30px;
    -fx-background-color: rgba(255, 255, 255, 0.9);
    -fx-background-radius: 5px;
    -fx-border-color: rgba(0, 0, 0, 0.1);
    -fx-border-radius: 5px;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 3);
    -fx-cursor: hand;
    -fx-focus-traversable: true;
    -fx-alignment: center;
}

.exit-fullscreen-button:hover {
    -fx-background-color: rgba(255, 255, 255, 1.0);
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 10, 0, 0, 4);
}

.exit-fullscreen-button:pressed {
    -fx-background-color: rgba(240, 240, 240, 0.95);
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 5, 0, 0, 2);
}

/* 普通按钮样式 - 降低优先级，避免覆盖特定按钮样式 */
Button {
    -fx-font-size: 14px;
    -fx-pref-height: 35px;
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #333;
    -fx-background-radius: 5px;
    -fx-border-color: #ccc;
    -fx-border-radius: 5px;
    -fx-border-width: 1px;
}

Button:hover {
    -fx-background-color: #e0e0e0;
}

Button:pressed {
    -fx-background-color: #d0d0d0;
}

/* 主要按钮样式 */
.button:default {
    -fx-background-color: #4CAF50;
    -fx-text-fill: white;
    -fx-border-color: #4CAF50;
}

.button:default:hover {
    -fx-background-color: #45a049;
    -fx-border-color: #45a049;
}

.button:default:pressed {
    -fx-background-color: #3d8b40;
    -fx-border-color: #3d8b40;
}

/* 输入框样式 */
.text-field {
    -fx-font-size: 14px;
    -fx-pref-height: 35px;
    -fx-background-color: white;
    -fx-border-color: #ccc;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-border-width: 1px;
}

.text-field:focused {
    -fx-border-color: #4CAF50;
    -fx-border-width: 2px;
}

/* 文本区域样式 */
.text-area {
    -fx-font-size: 14px;
    -fx-background-color: white;
    -fx-border-color: #ccc;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-border-width: 1px;
}

.text-area:focused {
    -fx-border-color: #4CAF50;
    -fx-border-width: 2px;
}

.text-area .content {
    -fx-background-color: white;
    -fx-background-radius: 5px;
}

/* 标签样式 */
.label {
    -fx-font-size: 14px;
    -fx-text-fill: #333;
}

/* 状态标签样式 */
.status-success {
    -fx-text-fill: #4CAF50;
    -fx-font-weight: bold;
}

.status-error {
    -fx-text-fill: #f44336;
    -fx-font-weight: bold;
}

/* 进度指示器样式 */
.progress-indicator {
    -fx-progress-color: #4CAF50;
}

/* 网格面板样式 */
.grid-pane {
    -fx-hgap: 10px;
    -fx-vgap: 15px;
}

/* 容器样式 */
.vbox, .hbox {
    -fx-spacing: 10px;
}

/* 外部应用程序按钮样式 */
.external-app-button {
    -fx-font-size: 14px;
    -fx-pref-width: 120px;
    -fx-pref-height: 40px;
    -fx-background-color: #FF9800;
    -fx-text-fill: white;
    -fx-background-radius: 5px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 5, 0, 0, 2);
}

.external-app-button:hover {
    -fx-background-color: #F57C00;
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.external-app-button:pressed {
    -fx-background-color: #E65100;
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* 主页标题样式 */
.main-page-title {
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-alignment: center;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
    -fx-wrap-text: true;
    -fx-max-width: Infinity;
}

/* 主页内容区域样式 */
.main-content-area {
    -fx-background-color: rgba(255, 255, 255, 0.05);
    -fx-background-radius: 10px;
    -fx-alignment: TOP_CENTER;
}

/* 根容器样式 */
.root {
    -fx-background-color: #f5f5f5;
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
}
